fail: 2025-07-21 11:07:53.9409996 +08:00 Monday L System.Logging.StringLogging[0] #26 '00-2f3d7a90053ec302c7800cec49e9bd2c-95edd620659afc1b-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-21 11:07:53——错误SQL】
      
      [Sql]:SELECT `staff_code` FROM `meeting_staff`  WHERE  (`staff_code` IN ('20250205','20250206'))   AND ( `IsDelete` = @IsDelete1 )  
      [Pars]:
      [Name]:@IsDelete1 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 11:07:54.7301669 +08:00 Monday L Admin.NET.Application.MeetingStaffService[0] #26 '00-2f3d7a90053ec302c7800cec49e9bd2c-95edd620659afc1b-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      批量处理导入数据时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 344
         at MySqlConnector.MySqlCommand.ExecuteDbDataReader(CommandBehavior behavior) in /_/src/MySqlConnector/MySqlCommand.cs:line 278
         at SqlSugar.AdoProvider.GetDataReader(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetData[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToList[TResult]()
         at SqlSugar.QueryableProvider`1.ToList()
         at Admin.NET.Application.MeetingStaffService.<>c__DisplayClass18_0.<ImportData>b__1(List`1 pageItems) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 387
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 11:34:48.3446541 +08:00 Monday L System.Logging.StringLogging[0] #40 '00-99ed1ed0ab148965cd9a40bb48089500-93a72704dfe7a7e5-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-21 11:34:48——错误SQL】
      
      [Sql]:SELECT `staff_code` FROM `meeting_staff`  WHERE  (`staff_code` IN ('20250205','20250206'))   AND ( `IsDelete` = @IsDelete1 )  
      [Pars]:
      [Name]:@IsDelete1 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 11:34:50.0120000 +08:00 Monday L Admin.NET.Application.MeetingStaffService[0] #40 '00-99ed1ed0ab148965cd9a40bb48089500-93a72704dfe7a7e5-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      批量处理导入数据时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 344
         at MySqlConnector.MySqlCommand.ExecuteDbDataReader(CommandBehavior behavior) in /_/src/MySqlConnector/MySqlCommand.cs:line 278
         at SqlSugar.AdoProvider.GetDataReader(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetData[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToList[TResult]()
         at SqlSugar.QueryableProvider`1.ToList()
         at Admin.NET.Application.MeetingStaffService.<>c__DisplayClass18_0.<ImportData>b__1(List`1 pageItems) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 387
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 11:51:01.6967201 +08:00 Monday L System.Logging.StringLogging[0] #10 '00-5f2e5c83e1b4b5952e06a2679c5c529c-628fab766699e724-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-21 11:51:01——错误SQL】
      
      [Sql]:SELECT `staff_code` FROM `meeting_staff`  WHERE  (`staff_code` IN ('20250205','20250206'))   AND ( `IsDelete` = @IsDelete1 )  
      [Pars]:
      [Name]:@IsDelete1 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 11:51:02.0282650 +08:00 Monday L Admin.NET.Application.MeetingStaffService[0] #10 '00-5f2e5c83e1b4b5952e06a2679c5c529c-628fab766699e724-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      批量处理导入数据时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 344
         at MySqlConnector.MySqlCommand.ExecuteDbDataReader(CommandBehavior behavior) in /_/src/MySqlConnector/MySqlCommand.cs:line 278
         at SqlSugar.AdoProvider.GetDataReader(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetData[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToList[TResult]()
         at SqlSugar.QueryableProvider`1.ToList()
         at Admin.NET.Application.MeetingStaffService.<>c__DisplayClass18_0.<ImportData>b__1(List`1 pageItems) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 387
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 14:13:29.8828364 +08:00 Monday L System.Logging.StringLogging[0] #24 '00-4f6c0d4e3e928feeca8721acb09197fe-c4d64d79c83c8e81-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-21 14:13:29——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,@staff_name_2,@position_3,@department_4,@email_5,@phone_6,@avatar_url_7,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-21 14:13:27.118',NULL,N'1300000000101',@CreateUserName_8,NULL,NULL,N'700060488069189'),  (NULL,@staff_code_9,@staff_name_10,@position_11,@department_12,@email_13,@phone_14,@avatar_url_15,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-21 14:13:27.169',NULL,N'1300000000101',@CreateUserName_16,NULL,NULL,N'700060488290373') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@staff_name_2 [Value]:钟奥键 [Type]:String    
      [Name]:@position_3 [Value]:测试 [Type]:String    
      [Name]:@department_4 [Value]:测试 [Type]:String    
      [Name]:@email_5 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_6 [Value]:13546947512 [Type]:String    
      [Name]:@avatar_url_7 [Value]: [Type]:String    
      [Name]:@CreateUserName_8 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_9 [Value]:20250206 [Type]:String    
      [Name]:@staff_name_10 [Value]:任瑞颖 [Type]:String    
      [Name]:@position_11 [Value]:测试 [Type]:String    
      [Name]:@department_12 [Value]:测试 [Type]:String    
      [Name]:@email_13 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_14 [Value]:13546947512 [Type]:String    
      [Name]:@avatar_url_15 [Value]: [Type]:String    
      [Name]:@CreateUserName_16 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 14:13:31.8259661 +08:00 Monday L Admin.NET.Application.MeetingStaffService[0] #24 '00-4f6c0d4e3e928feeca8721acb09197fe-c4d64d79c83c8e81-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      批量处理导入数据时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
         at SqlSugar.AdoProvider.ExecuteCommand(String sql, SugarParameter[] parameters)
         at SqlSugar.InsertableProvider`1.ExecuteCommand()
         at Admin.NET.Application.MeetingStaffService.<>c__DisplayClass18_0.<ImportData>b__1(List`1 pageItems) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 481
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
